[20:19:49.368] Running build in Washington, D.C., USA (East) – iad1
[20:19:49.368] Build machine configuration: 2 cores, 8 GB
[20:19:49.382] Cloning github.com/Lziu/YesPlayMusic (Branch: master, Commit: 4b3166f)
[20:19:49.516] Previous build caches not available
[20:19:49.877] Cloning completed: 495.000ms
[20:19:50.755] Running "vercel build"
[20:19:51.399] Vercel CLI 46.0.2
[20:19:52.048] Installing dependencies...
[20:19:52.442] yarn install v1.22.19
[20:19:52.537] [1/5] Validating package.json...
[20:19:52.541] [2/5] Resolving packages...
[20:19:52.892] warning Resolution field "degenerator@2.2.0" is incompatible with requested version "degenerator@^3.0.2"
[20:19:52.950] warning Resolution field "icon-gen@3.0.0" is incompatible with requested version "icon-gen@^2.0.0"
[20:19:53.364] [3/5] Fetching packages...
[20:20:13.543] [4/5] Linking dependencies...
[20:20:13.549] warning "svg-sprite-loader > svg-baker > postcss-prefix-selector@1.14.0" has incorrect peer dependency "postcss@7.x || 8.x".
[20:20:13.563] warning " > eslint-config-prettier@8.5.0" has incorrect peer dependency "eslint@>=7.0.0".
[20:20:13.564] warning " > sass-loader@10.2.1" has unmet peer dependency "webpack@^4.36.0 || ^5.0.0".
[20:20:22.573] [5/5] Building fresh packages...
[20:20:33.235] warning Error running install script for optional dependency: "/vercel/path0/node_modules/abstract-socket: Command failed.
[20:20:33.236] Exit code: 1
[20:20:33.237] Command: node-gyp rebuild
[20:20:33.237] Arguments: 
[20:20:33.237] Directory: /vercel/path0/node_modules/abstract-socket
[20:20:33.237] Output:
[20:20:33.237] gyp info it worked if it ends with ok
[20:20:33.238] gyp info using node-gyp@11.2.0
[20:20:33.238] gyp info using node@22.18.0 | linux | x64
[20:20:33.238] gyp info find Python using Python version 3.12.2 found at \"/usr/local/bin/python3\"
[20:20:33.238] 
[20:20:33.239] gyp http GET https://nodejs.org/download/release/v22.18.0/node-v22.18.0-headers.tar.gz
[20:20:33.239] gyp http 200 https://nodejs.org/download/release/v22.18.0/node-v22.18.0-headers.tar.gz
[20:20:33.239] gyp http GET https://nodejs.org/download/release/v22.18.0/SHASUMS256.txt
[20:20:33.240] gyp http 200 https://nodejs.org/download/release/v22.18.0/SHASUMS256.txt
[20:20:33.241] gyp info spawn /usr/local/bin/python3
[20:20:33.241] gyp info spawn args [
[20:20:33.241] gyp info spawn args '/node22/lib/node_modules/npm/node_modules/node-gyp/gyp/gyp_main.py',
[20:20:33.241] gyp info spawn args 'binding.gyp',
[20:20:33.242] gyp info spawn args '-f',
[20:20:33.243] gyp info spawn args 'make',
[20:20:33.243] gyp info spawn args '-I',
[20:20:33.243] gyp info spawn args '/vercel/path0/node_modules/abstract-socket/build/config.gypi',
[20:20:33.243] gyp info spawn args '-I',
[20:20:33.244] gyp info spawn args '/node22/lib/node_modules/npm/node_modules/node-gyp/addon.gypi',
[20:20:33.244] gyp info spawn args '-I',
[20:20:33.244] gyp info spawn args '/vercel/.cache/node-gyp/22.18.0/include/node/common.gypi',
[20:20:33.245] gyp info spawn args '-Dlibrary=shared_library',
[20:20:33.245] gyp info spawn args '-Dvisibility=default',
[20:20:33.245] gyp info spawn args '-Dnode_root_dir=/vercel/.cache/node-gyp/22.18.0',
[20:20:33.245] gyp info spawn args '-Dnode_gyp_dir=/node22/lib/node_modules/npm/node_modules/node-gyp',
[20:20:33.245] gyp info spawn args '-Dnode_lib_file=/vercel/.cache/node-gyp/22.18.0/<(target_arch)/node.lib',
[20:20:33.246] gyp info spawn args '-Dmodule_root_dir=/vercel/path0/node_modules/abstract-socket',
[20:20:33.246] gyp info spawn args '-Dnode_engine=v8',
[20:20:33.246] gyp info spawn args '--depth=.',
[20:20:33.247] gyp info spawn args '--no-parallel',
[20:20:33.247] gyp info spawn args '--generator-output',
[20:20:33.248] gyp info spawn args 'build',
[20:20:33.249] gyp info spawn args '-Goutput_dir=.'
[20:20:33.249] gyp info spawn args ]
[20:20:33.249] gyp info spawn make
[20:20:33.249] gyp info spawn args [ 'BUILDTYPE=Release', '-C', 'build' ]
[20:20:33.250] make: Entering directory '/vercel/path0/node_modules/abstract-socket/build'
[20:20:33.250]   CXX(target) Release/obj.target/bindings/src/abstract_socket.o
[20:20:33.252] In file included from ../../nan/nan.h:180,
[20:20:33.252]                  from ../src/abstract_socket.cc:5:
[20:20:33.255] ../../nan/nan_callbacks.h:55:23: error: 'AccessorSignature' is not a member of 'v8'
[20:20:33.256]    55 | typedef v8::Local<v8::AccessorSignature> Sig;
[20:20:33.256]       |                       ^~~~~~~~~~~~~~~~~
[20:20:33.257] ../../nan/nan_callbacks.h:55:40: error: template argument 1 is invalid
[20:20:33.257]    55 | typedef v8::Local<v8::AccessorSignature> Sig;
[20:20:33.257]       |                                        ^
[20:20:33.257] In file included from ../src/abstract_socket.cc:5:
[20:20:33.258] ../../nan/nan.h: In function 'void Nan::SetAccessor(v8::Local<v8::ObjectTemplate>, v8::Local<v8::String>, Nan::GetterCallback, Nan::SetterCallback, v8::Local<v8::Value>, v8::AccessControl, v8::PropertyAttribute, Nan::imp::Sig)':
[20:20:33.258] ../../nan/nan.h:2546:19: error: no matching function for call to 'v8::ObjectTemplate::SetAccessor(v8::Local<v8::String>&, void (*&)(v8::Local<v8::Name>, const v8::PropertyCallbackInfo<v8::Value>&), void (*&)(v8::Local<v8::Name>, v8::Local<v8::Value>, const v8::PropertyCallbackInfo<void>&), v8::Local<v8::Object>&, v8::AccessControl&, v8::PropertyAttribute&)'
[20:20:33.258]  2546 |   tpl->SetAccessor(
[20:20:33.258]       |   ~~~~~~~~~~~~~~~~^
[20:20:33.258]  2547 |       name
[20:20:33.259]       |       ~~~~         
[20:20:33.265]  2548 |     , getter_
[20:20:33.265]       |     ~~~~~~~~~      
[20:20:33.265]  2549 |     , setter_
[20:20:33.265]       |     ~~~~~~~~~      
[20:20:33.266]  2550 |     , obj
[20:20:33.266]       |     ~~~~~          
[20:20:33.266]  2551 |     , settings
[20:20:33.266]       |     ~~~~~~~~~~     
[20:20:33.266]  2552 |     , attribute
[20:20:33.267]       |     ~~~~~~~~~~~    
[20:20:33.267]  2553 | #if (NODE_MODULE_VERSION < NODE_18_0_MODULE_VERSION)
[20:20:33.267]       | ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[20:20:33.267]  2554 |     , signature
[20:20:33.269]       |     ~~~~~~~~~~~    
[20:20:33.269]  2555 | #endif
[20:20:33.269]       | ~~~~~~             
[20:20:33.270]  2556 |   );
[20:20:33.270]       |   ~                
[20:20:33.270] In file included from /vercel/.cache/node-gyp/22.18.0/include/node/v8-function.h:15,
[20:20:33.270]                  from /vercel/.cache/node-gyp/22.18.0/include/node/v8.h:33,
[20:20:33.270]                  from /vercel/.cache/node-gyp/22.18.0/include/node/node.h:74,
[20:20:33.271]                  from ../../nan/nan.h:60,
[20:20:33.271]                  from ../src/abstract_socket.cc:5:
[20:20:33.271] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1049:8: note: candidate: 'void v8::ObjectTemplate::SetAccessor(v8::Local<v8::String>, v8::AccessorGetterCallback, v8::AccessorSetterCallback, v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)'
[20:20:33.271]  1049 |   void SetAccessor(
[20:20:33.271]       |        ^~~~~~~~~~~
[20:20:33.272] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1052:61: note:   no known conversion for argument 5 from 'v8::AccessControl' to 'v8::PropertyAttribute'
[20:20:33.272]  1052 |       Local<Value> data = Local<Value>(), PropertyAttribute attribute = None,
[20:20:33.272]       |                                           ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~
[20:20:33.272] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1055:8: note: candidate: 'void v8::ObjectTemplate::SetAccessor(v8::Local<v8::Name>, v8::AccessorNameGetterCallback, v8::AccessorNameSetterCallback, v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)'
[20:20:33.272]  1055 |   void SetAccessor(
[20:20:33.273]       |        ^~~~~~~~~~~
[20:20:33.273] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1058:61: note:   no known conversion for argument 5 from 'v8::AccessControl' to 'v8::PropertyAttribute'
[20:20:33.273]  1058 |       Local<Value> data = Local<Value>(), PropertyAttribute attribute = None,
[20:20:33.273]       |                                           ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~
[20:20:33.273] In file included from ../../nan/nan.h:60,
[20:20:33.273]                  from ../src/abstract_socket.cc:5:
[20:20:33.274] ../src/abstract_socket.cc: At global scope:
[20:20:33.274] /vercel/.cache/node-gyp/22.18.0/include/node/node.h:1228:7: warning: cast between incompatible function types from 'void (*)(v8::Local<v8::Object>)' to 'node::addon_register_func' {aka 'void (*)(v8::Local<v8::Object>, v8::Local<v8::Value>, void*)'} [-Wcast-function-type]
[20:20:33.274]  1228 |       (node::addon_register_func) (regfunc),                          \\\n      |       ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[20:20:33.274] /vercel/.cache/node-gyp/22.18.0/include/node/node.h:1262:3: note: in expansion of macro 'NODE_MODULE_X'
[20:20:33.275]  1262 |   NODE_MODULE_X(modname, regfunc, NULL, 0)  // NOLINT (readability/null_usage)
[20:20:33.275]       |   ^~~~~~~~~~~~~
[20:20:33.275] ../src/abstract_socket.cc:181:1: note: in expansion of macro 'NODE_MODULE'
[20:20:33.275]   181 | NODE_MODULE(abstract_socket, Initialize)
[20:20:33.275]       | ^~~~~~~~~~~
[20:20:33.275] In file included from /vercel/.cache/node-gyp/22.18.0/include/node/v8-array-buffer.h:12,
[20:20:33.276]                  from /vercel/.cache/node-gyp/22.18.0/include/node/v8.h:24,
[20:20:33.276]                  from /vercel/.cache/node-gyp/22.18.0/include/node/node.h:74,
[20:20:33.276]                  from ../../nan/nan.h:60,
[20:20:33.276]                  from ../src/abstract_socket.cc:5:
[20:20:33.276] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h: In instantiation of 'v8::Local<T>::Local(v8::Local<S>) [with S = v8::Data; T = v8::Value]':
[20:20:33.277] ../../nan/nan_callbacks_12_inl.h:175:53:   required from here
[20:20:33.277] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h:269:42: error: static assertion failed: type check
[20:20:33.277]   269 |     static_assert(std::is_base_of<T, S>::value, \"type check\");
[20:20:33.277]       |                                          ^~~~~
[20:20:33.277] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h:269:42: note: 'std::integral_constant<bool, false>::value' evaluates to false
[20:20:33.278] make: *** [bindings.target.mk:112: Release/obj.target/bindings/src/abstract_socket.o] Error 1
[20:20:33.278] make: Leaving directory '/vercel/path0/node_modules/abstract-socket/build'
[20:20:33.278] gyp ERR! build error 
[20:20:33.278] gyp ERR! stack Error: `make` failed with exit code: 2
[20:20:33.279] gyp ERR! stack at ChildProcess.<anonymous> (/node22/lib/node_modules/npm/node_modules/node-gyp/lib/build.js:219:23)
[20:20:33.279] gyp ERR! System Linux 5.10.174
[20:20:33.279] gyp ERR! command \"/node22/bin/node\" \"/node22/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js\" \"rebuild\"
[20:20:33.279] gyp ERR! cwd /vercel/path0/node_modules/abstract-socket
[20:20:33.279] gyp ERR! node -v v22.18.0
[20:20:33.280] gyp ERR! node-gyp -v v11.2.0
[20:20:33.280] gyp ERR! not ok"
[20:20:33.280] info This module is OPTIONAL, you can safely ignore this error
[20:20:34.460] success Saved lockfile.
[20:20:34.464] $ electron-builder install-app-deps
[20:20:34.744]   • electron-builder  version=23.0.3
[20:20:34.807]   • rebuilding native dependencies  dependencies=register-scheme@0.0.2, sharp@0.29.3 platform=linux arch=x64
[20:20:34.808]   • install prebuilt binary  name=sharp version=0.29.3 platform=linux arch=x64 napi= 
[20:20:34.939]   • rebuilding native dependency  name=register-scheme version=0.0.2
[20:20:36.729] Done in 44.29s.
[20:20:36.766] Running "yarn run build"
[20:20:36.939] yarn run v1.22.19
[20:20:36.967] $ vue-cli-service build
[20:20:37.484] Browserslist: caniuse-lite is outdated. Please run:
[20:20:37.485]   npx browserslist@latest --update-db
[20:20:37.485]   Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[20:20:37.517] 
[20:20:37.517] -  Building for production...
[20:20:39.884] Browserslist: caniuse-lite is outdated. Please run:
[20:20:39.885]   npx browserslist@latest --update-db
[20:20:39.885]   Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[20:20:39.900] (node:698) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
[20:20:39.900] (Use `node --trace-deprecation ...` to show where the warning was created)
[20:21:03.381]  WARNING  Compiled with 15 warnings12:21:03 PM
[20:21:03.382] 
[20:21:03.383] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.383] 
[20:21:03.384] /vercel/path0/src/components/CoverRow.vue
[20:21:03.384]   33:17  warning  'v-html' directive can lead to XSS attack  vue/no-v-html
[20:21:03.384] 
[20:21:03.384] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.384] 
[20:21:03.385] 
[20:21:03.385] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.385] 
[20:21:03.385] /vercel/path0/src/components/Modal.vue
[20:21:03.385]   25:5  warning  Prop 'close' requires default value to be set  vue/require-default-prop
[20:21:03.385] 
[20:21:03.386] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.386] 
[20:21:03.386] 
[20:21:03.386] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.386] 
[20:21:03.386] /vercel/path0/src/components/MvRow.vue
[20:21:03.387]   23:29  warning  'v-html' directive can lead to XSS attack    vue/no-v-html
[20:21:03.388]   33:5   warning  Prop 'mvs' requires default value to be set  vue/require-default-prop
[20:21:03.388] 
[20:21:03.388] ✖ 2 problems (0 errors, 2 warnings)
[20:21:03.388] 
[20:21:03.389] 
[20:21:03.389] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.389] 
[20:21:03.389] /vercel/path0/src/components/Navbar.vue
[20:21:03.390]   50:11  warning  Attribute "loading" should go before "@click"  vue/attributes-order
[20:21:03.390] 
[20:21:03.390] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.390]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:21:03.390] 
[20:21:03.391] 
[20:21:03.392] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.392] 
[20:21:03.392] /vercel/path0/src/components/TrackListItem.vue
[20:21:03.392]   98:5  warning  Prop 'trackProp' requires default value to be set  vue/require-default-prop
[20:21:03.392] 
[20:21:03.393] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.394] 
[20:21:03.394] 
[20:21:03.394] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.394] 
[20:21:03.394] /vercel/path0/src/views/loginAccount.vue
[20:21:03.394]   105:9  warning  'v-html' directive can lead to XSS attack  vue/no-v-html
[20:21:03.395] 
[20:21:03.395] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.395] 
[20:21:03.395] 
[20:21:03.395] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:21:03.396] 
[20:21:03.396] /vercel/path0/src/views/mv.vue
[20:21:03.396]   59:9  warning  Property name "mv" is not PascalCase  vue/component-definition-name-casing
[20:21:03.396] 
[20:21:03.396] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.396]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:21:03.397] 
[20:21:03.397] 
[20:21:03.397] You may use special comments to disable some warnings.
[20:21:03.397] Use // eslint-disable-next-line to ignore the next line.
[20:21:03.397] Use /* eslint-disable */ to ignore all warnings in a file.
[20:21:03.397]  warning  in ./src/assets/icons/index.js
[20:21:03.398] 
[20:21:03.398] Module Warning (from ./node_modules/thread-loader/dist/cjs.js):
[20:21:03.398] 
[20:21:03.398] /vercel/path0/src/assets/icons/index.js
[20:21:03.398]   4:15  warning  Property name "svg-icon" is not PascalCase  vue/component-definition-name-casing
[20:21:03.400] 
[20:21:03.400] ✖ 1 problem (0 errors, 1 warning)
[20:21:03.400]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:21:03.401] 
[20:21:03.401] 
[20:21:03.401]  @ ./src/main.js 16:0-24
[20:21:03.401]  @ multi ./src/main.js
[20:21:03.401] 
[20:21:03.401]  warning  
[20:21:03.404] 
[20:21:03.404] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:21:03.405] Conflicting order. Following module has been added:
[20:21:03.405]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ArtistsInLine.vue?vue&type=style&index=0&id=b3c008da&lang=scss&scoped=true&
[20:21:03.405] despite it was not able to fulfill desired ordering with these modules:
[20:21:03.405]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/CoverRow.vue?vue&type=style&index=0&id=46c6acd8&lang=scss&scoped=true&
[20:21:03.405]    - couldn't fulfill desired order of chunk group(s) 
[20:21:03.405]    - while fulfilling desired order of chunk group(s) , , , , 
[20:21:03.406]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/Cover.vue?vue&type=style&index=0&id=402f8e43&lang=scss&scoped=true&
[20:21:03.406]    - couldn't fulfill desired order of chunk group(s) 
[20:21:03.406]    - while fulfilling desired order of chunk group(s) , , , , , , 
[20:21:03.406]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:21:03.406]    - couldn't fulfill desired order of chunk group(s) , , , 
[20:21:03.407] 
[20:21:03.407]  warning  
[20:21:03.407] 
[20:21:03.407] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:21:03.407] Conflicting order. Following module has been added:
[20:21:03.407]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/TrackListItem.vue?vue&type=style&index=0&id=24ccaa64&lang=scss&scoped=true&
[20:21:03.408] despite it was not able to fulfill desired ordering with these modules:
[20:21:03.408]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:21:03.408]    - couldn't fulfill desired order of chunk group(s) , , , 
[20:21:03.408] 
[20:21:03.408]  warning  
[20:21:03.408] 
[20:21:03.408] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:21:03.409] Conflicting order. Following module has been added:
[20:21:03.409]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/Cover.vue?vue&type=style&index=0&id=402f8e43&lang=scss&scoped=true&
[20:21:03.409] despite it was not able to fulfill desired ordering with these modules:
[20:21:03.409]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:21:03.409]    - couldn't fulfill desired order of chunk group(s) , 
[20:21:03.409]    - while fulfilling desired order of chunk group(s) , 
[20:21:03.409]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:21:03.410]    - couldn't fulfill desired order of chunk group(s) , , , , 
[20:21:03.410] 
[20:21:03.410]  warning  
[20:21:03.410] 
[20:21:03.410] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:21:03.410] Conflicting order. Following module has been added:
[20:21:03.410]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/CoverRow.vue?vue&type=style&index=0&id=46c6acd8&lang=scss&scoped=true&
[20:21:03.411] despite it was not able to fulfill desired ordering with these modules:
[20:21:03.411]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:21:03.411]    - couldn't fulfill desired order of chunk group(s) , 
[20:21:03.411]    - while fulfilling desired order of chunk group(s) , 
[20:21:03.411]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:21:03.411]    - couldn't fulfill desired order of chunk group(s) , , 
[20:21:03.411] 
[20:21:03.412]  warning  
[20:21:03.412] 
[20:21:03.412] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:21:03.412] Conflicting order. Following module has been added:
[20:21:03.412]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:21:03.412] despite it was not able to fulfill desired ordering with these modules:
[20:21:03.412]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:21:03.412]    - couldn't fulfill desired order of chunk group(s) , 
[20:21:03.413] 
[20:21:03.413]  warning  
[20:21:03.413] 
[20:21:03.413] asset size limit: The following asset(s) exceed the recommended size limit (244 KiB).
[20:21:03.413] This can impact web performance.
[20:21:03.413] Assets: 
[20:21:03.413]   js/chunk-d87ab370.e44aceb0.js (371 KiB)
[20:21:03.414]   js/chunk-vendors.b1d9aaa8.js (617 KiB)
[20:21:03.414]   img/icons/1024x1024.png (381 KiB)
[20:21:03.414]   img/icons/icon.ico (353 KiB)
[20:21:03.414]   img/icons/icon.icns (765 KiB)
[20:21:03.414] 
[20:21:03.414]  warning  
[20:21:03.414] 
[20:21:03.415] entrypoint size limit: The following entrypoint(s) combined asset size exceeds the recommended limit (244 KiB). This can impact web performance.
[20:21:03.415] Entrypoints:
[20:21:03.415]   index (859 KiB)
[20:21:03.415]       css/chunk-vendors.46ab30d4.css
[20:21:03.415]       js/chunk-vendors.b1d9aaa8.js
[20:21:03.415]       css/index.705fc877.css
[20:21:03.415]       js/index.9e42d3c2.js
[20:21:03.416] 
[20:21:03.416] 
[20:21:03.438]   File                                      Size             Gzipped
[20:21:03.439] 
[20:21:03.439]   dist/js/chunk-vendors.b1d9aaa8.js         616.79 KiB       195.79 KiB
[20:21:03.440]   dist/js/chunk-d87ab370.e44aceb0.js        371.24 KiB       108.88 KiB
[20:21:03.440]   dist/js/index.9e42d3c2.js                 194.19 KiB       48.44 KiB
[20:21:03.440]   dist/precache-manifest.25184ee2735f25b    3.81 KiB         1.18 KiB
[20:21:03.440]   d9dffc4198c6a7c45.js
[20:21:03.440]   dist/service-worker.js                    1.04 KiB         0.61 KiB
[20:21:03.441]   dist/css/chunk-d87ab370.7c372010.css      81.10 KiB        12.54 KiB
[20:21:03.441]   dist/css/index.705fc877.css               29.76 KiB        5.71 KiB
[20:21:03.441]   dist/css/chunk-vendors.46ab30d4.css       18.29 KiB        3.61 KiB
[20:21:03.441] 
[20:21:03.441]   Images and other types of assets omitted.
[20:21:03.442] 
[20:21:03.442]  DONE  Build complete. The dist directory is ready to be deployed.
[20:21:03.442]  INFO  Check out deployment instructions at https://cli.vuejs.org/guide/deployment.html
[20:21:03.442]       
[20:21:04.457] Done in 27.52s.
[20:21:04.598] Build Completed in /vercel/output [1m]
[20:21:04.711] Deploying outputs...
[20:21:07.695] Deployment completed
[20:21:08.537] Creating build cache...
[20:21:35.122] Created build cache: 26.583s
[20:21:35.122] Uploading build cache [201.70 MB]