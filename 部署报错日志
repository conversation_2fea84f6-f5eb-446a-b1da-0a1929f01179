[20:31:36.294] Running build in Washington, D.C., USA (East) – iad1
[20:31:36.295] Build machine configuration: 2 cores, 8 GB
[20:31:36.308] Cloning github.com/Lziu/YesPlayMusic (Branch: master, Commit: 88fefb3)
[20:31:36.437] Previous build caches not available
[20:31:36.804] Cloning completed: 496.000ms
[20:31:37.655] Running "vercel build"
[20:31:38.050] Vercel CLI 46.0.2
[20:31:38.646] Installing dependencies...
[20:31:39.030] yarn install v1.22.19
[20:31:39.118] [1/5] Validating package.json...
[20:31:39.121] [2/5] Resolving packages...
[20:31:39.866] warning Resolution field "degenerator@2.2.0" is incompatible with requested version "degenerator@^3.0.2"
[20:31:39.914] warning Resolution field "icon-gen@3.0.0" is incompatible with requested version "icon-gen@^2.0.0"
[20:31:40.288] [3/5] Fetching packages...
[20:31:59.275] [4/5] Linking dependencies...
[20:31:59.282] warning "svg-sprite-loader > svg-baker > postcss-prefix-selector@1.14.0" has incorrect peer dependency "postcss@7.x || 8.x".
[20:31:59.291] warning " > eslint-config-prettier@8.5.0" has incorrect peer dependency "eslint@>=7.0.0".
[20:31:59.291] warning " > sass-loader@10.2.1" has unmet peer dependency "webpack@^4.36.0 || ^5.0.0".
[20:32:07.532] [5/5] Building fresh packages...
[20:32:15.881] warning Error running install script for optional dependency: "/vercel/path0/node_modules/abstract-socket: Command failed.
[20:32:15.881] Exit code: 1
[20:32:15.882] Command: node-gyp rebuild
[20:32:15.882] Arguments: 
[20:32:15.882] Directory: /vercel/path0/node_modules/abstract-socket
[20:32:15.883] Output:
[20:32:15.883] gyp info it worked if it ends with ok
[20:32:15.883] gyp info using node-gyp@11.2.0
[20:32:15.884] gyp info using node@22.18.0 | linux | x64
[20:32:15.884] gyp info find Python using Python version 3.12.2 found at \"/usr/local/bin/python3\"
[20:32:15.884] 
[20:32:15.885] gyp http GET https://nodejs.org/download/release/v22.18.0/node-v22.18.0-headers.tar.gz
[20:32:15.885] gyp http 200 https://nodejs.org/download/release/v22.18.0/node-v22.18.0-headers.tar.gz
[20:32:15.886] gyp http GET https://nodejs.org/download/release/v22.18.0/SHASUMS256.txt
[20:32:15.886] gyp http 200 https://nodejs.org/download/release/v22.18.0/SHASUMS256.txt
[20:32:15.886] gyp info spawn /usr/local/bin/python3
[20:32:15.887] gyp info spawn args [
[20:32:15.887] gyp info spawn args '/node22/lib/node_modules/npm/node_modules/node-gyp/gyp/gyp_main.py',
[20:32:15.887] gyp info spawn args 'binding.gyp',
[20:32:15.887] gyp info spawn args '-f',
[20:32:15.887] gyp info spawn args 'make',
[20:32:15.887] gyp info spawn args '-I',
[20:32:15.887] gyp info spawn args '/vercel/path0/node_modules/abstract-socket/build/config.gypi',
[20:32:15.888] gyp info spawn args '-I',
[20:32:15.888] gyp info spawn args '/node22/lib/node_modules/npm/node_modules/node-gyp/addon.gypi',
[20:32:15.888] gyp info spawn args '-I',
[20:32:15.888] gyp info spawn args '/vercel/.cache/node-gyp/22.18.0/include/node/common.gypi',
[20:32:15.888] gyp info spawn args '-Dlibrary=shared_library',
[20:32:15.888] gyp info spawn args '-Dvisibility=default',
[20:32:15.888] gyp info spawn args '-Dnode_root_dir=/vercel/.cache/node-gyp/22.18.0',
[20:32:15.888] gyp info spawn args '-Dnode_gyp_dir=/node22/lib/node_modules/npm/node_modules/node-gyp',
[20:32:15.888] gyp info spawn args '-Dnode_lib_file=/vercel/.cache/node-gyp/22.18.0/<(target_arch)/node.lib',
[20:32:15.888] gyp info spawn args '-Dmodule_root_dir=/vercel/path0/node_modules/abstract-socket',
[20:32:15.888] gyp info spawn args '-Dnode_engine=v8',
[20:32:15.889] gyp info spawn args '--depth=.',
[20:32:15.889] gyp info spawn args '--no-parallel',
[20:32:15.889] gyp info spawn args '--generator-output',
[20:32:15.889] gyp info spawn args 'build',
[20:32:15.889] gyp info spawn args '-Goutput_dir=.'
[20:32:15.889] gyp info spawn args ]
[20:32:15.889] gyp info spawn make
[20:32:15.889] gyp info spawn args [ 'BUILDTYPE=Release', '-C', 'build' ]
[20:32:15.889] make: Entering directory '/vercel/path0/node_modules/abstract-socket/build'
[20:32:15.889]   CXX(target) Release/obj.target/bindings/src/abstract_socket.o
[20:32:15.889] In file included from ../../nan/nan.h:180,
[20:32:15.889]                  from ../src/abstract_socket.cc:5:
[20:32:15.892] ../../nan/nan_callbacks.h:55:23: error: 'AccessorSignature' is not a member of 'v8'
[20:32:15.893]    55 | typedef v8::Local<v8::AccessorSignature> Sig;
[20:32:15.893]       |                       ^~~~~~~~~~~~~~~~~
[20:32:15.893] ../../nan/nan_callbacks.h:55:40: error: template argument 1 is invalid
[20:32:15.893]    55 | typedef v8::Local<v8::AccessorSignature> Sig;
[20:32:15.894]       |                                        ^
[20:32:15.894] In file included from ../src/abstract_socket.cc:5:
[20:32:15.894] ../../nan/nan.h: In function 'void Nan::SetAccessor(v8::Local<v8::ObjectTemplate>, v8::Local<v8::String>, Nan::GetterCallback, Nan::SetterCallback, v8::Local<v8::Value>, v8::AccessControl, v8::PropertyAttribute, Nan::imp::Sig)':
[20:32:15.895] ../../nan/nan.h:2546:19: error: no matching function for call to 'v8::ObjectTemplate::SetAccessor(v8::Local<v8::String>&, void (*&)(v8::Local<v8::Name>, const v8::PropertyCallbackInfo<v8::Value>&), void (*&)(v8::Local<v8::Name>, v8::Local<v8::Value>, const v8::PropertyCallbackInfo<void>&), v8::Local<v8::Object>&, v8::AccessControl&, v8::PropertyAttribute&)'
[20:32:15.895]  2546 |   tpl->SetAccessor(
[20:32:15.895]       |   ~~~~~~~~~~~~~~~~^
[20:32:15.895]  2547 |       name
[20:32:15.896]       |       ~~~~         
[20:32:15.896]  2548 |     , getter_
[20:32:15.899]       |     ~~~~~~~~~      
[20:32:15.899]  2549 |     , setter_
[20:32:15.899]       |     ~~~~~~~~~      
[20:32:15.899]  2550 |     , obj
[20:32:15.900]       |     ~~~~~          
[20:32:15.900]  2551 |     , settings
[20:32:15.900]       |     ~~~~~~~~~~     
[20:32:15.902]  2552 |     , attribute
[20:32:15.902]       |     ~~~~~~~~~~~    
[20:32:15.902]  2553 | #if (NODE_MODULE_VERSION < NODE_18_0_MODULE_VERSION)
[20:32:15.902]       | ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[20:32:15.903]  2554 |     , signature
[20:32:15.903]       |     ~~~~~~~~~~~    
[20:32:15.903]  2555 | #endif
[20:32:15.903]       | ~~~~~~             
[20:32:15.906]  2556 |   );
[20:32:15.906]       |   ~                
[20:32:15.906] In file included from /vercel/.cache/node-gyp/22.18.0/include/node/v8-function.h:15,
[20:32:15.906]                  from /vercel/.cache/node-gyp/22.18.0/include/node/v8.h:33,
[20:32:15.906]                  from /vercel/.cache/node-gyp/22.18.0/include/node/node.h:74,
[20:32:15.906]                  from ../../nan/nan.h:60,
[20:32:15.906]                  from ../src/abstract_socket.cc:5:
[20:32:15.907] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1049:8: note: candidate: 'void v8::ObjectTemplate::SetAccessor(v8::Local<v8::String>, v8::AccessorGetterCallback, v8::AccessorSetterCallback, v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)'
[20:32:15.907]  1049 |   void SetAccessor(
[20:32:15.907]       |        ^~~~~~~~~~~
[20:32:15.907] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1052:61: note:   no known conversion for argument 5 from 'v8::AccessControl' to 'v8::PropertyAttribute'
[20:32:15.907]  1052 |       Local<Value> data = Local<Value>(), PropertyAttribute attribute = None,
[20:32:15.907]       |                                           ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~
[20:32:15.907] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1055:8: note: candidate: 'void v8::ObjectTemplate::SetAccessor(v8::Local<v8::Name>, v8::AccessorNameGetterCallback, v8::AccessorNameSetterCallback, v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)'
[20:32:15.907]  1055 |   void SetAccessor(
[20:32:15.907]       |        ^~~~~~~~~~~
[20:32:15.907] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1058:61: note:   no known conversion for argument 5 from 'v8::AccessControl' to 'v8::PropertyAttribute'
[20:32:15.907]  1058 |       Local<Value> data = Local<Value>(), PropertyAttribute attribute = None,
[20:32:15.907]       |                                           ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~
[20:32:15.907] In file included from ../../nan/nan.h:60,
[20:32:15.907]                  from ../src/abstract_socket.cc:5:
[20:32:15.907] ../src/abstract_socket.cc: At global scope:
[20:32:15.907] /vercel/.cache/node-gyp/22.18.0/include/node/node.h:1228:7: warning: cast between incompatible function types from 'void (*)(v8::Local<v8::Object>)' to 'node::addon_register_func' {aka 'void (*)(v8::Local<v8::Object>, v8::Local<v8::Value>, void*)'} [-Wcast-function-type]
[20:32:15.907]  1228 |       (node::addon_register_func) (regfunc),                          \\\n      |       ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[20:32:15.907] /vercel/.cache/node-gyp/22.18.0/include/node/node.h:1262:3: note: in expansion of macro 'NODE_MODULE_X'
[20:32:15.907]  1262 |   NODE_MODULE_X(modname, regfunc, NULL, 0)  // NOLINT (readability/null_usage)
[20:32:15.907]       |   ^~~~~~~~~~~~~
[20:32:15.907] ../src/abstract_socket.cc:181:1: note: in expansion of macro 'NODE_MODULE'
[20:32:15.907]   181 | NODE_MODULE(abstract_socket, Initialize)
[20:32:15.907]       | ^~~~~~~~~~~
[20:32:15.907] In file included from /vercel/.cache/node-gyp/22.18.0/include/node/v8-array-buffer.h:12,
[20:32:15.908]                  from /vercel/.cache/node-gyp/22.18.0/include/node/v8.h:24,
[20:32:15.908]                  from /vercel/.cache/node-gyp/22.18.0/include/node/node.h:74,
[20:32:15.908]                  from ../../nan/nan.h:60,
[20:32:15.908]                  from ../src/abstract_socket.cc:5:
[20:32:15.908] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h: In instantiation of 'v8::Local<T>::Local(v8::Local<S>) [with S = v8::Data; T = v8::Value]':
[20:32:15.908] ../../nan/nan_callbacks_12_inl.h:175:53:   required from here
[20:32:15.908] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h:269:42: error: static assertion failed: type check
[20:32:15.908]   269 |     static_assert(std::is_base_of<T, S>::value, \"type check\");
[20:32:15.908]       |                                          ^~~~~
[20:32:15.908] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h:269:42: note: 'std::integral_constant<bool, false>::value' evaluates to false
[20:32:15.908] make: *** [bindings.target.mk:112: Release/obj.target/bindings/src/abstract_socket.o] Error 1
[20:32:15.908] make: Leaving directory '/vercel/path0/node_modules/abstract-socket/build'
[20:32:15.908] gyp ERR! build error 
[20:32:15.908] gyp ERR! stack Error: `make` failed with exit code: 2
[20:32:15.908] gyp ERR! stack at ChildProcess.<anonymous> (/node22/lib/node_modules/npm/node_modules/node-gyp/lib/build.js:219:23)
[20:32:15.908] gyp ERR! System Linux 5.10.174
[20:32:15.909] gyp ERR! command \"/node22/bin/node\" \"/node22/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js\" \"rebuild\"
[20:32:15.909] gyp ERR! cwd /vercel/path0/node_modules/abstract-socket
[20:32:15.909] gyp ERR! node -v v22.18.0
[20:32:15.909] gyp ERR! node-gyp -v v11.2.0
[20:32:15.909] gyp ERR! not ok"
[20:32:15.909] info This module is OPTIONAL, you can safely ignore this error
[20:32:16.264] success Saved lockfile.
[20:32:16.277] $ electron-builder install-app-deps
[20:32:16.545]   • electron-builder  version=23.0.3
[20:32:16.605]   • rebuilding native dependencies  dependencies=register-scheme@0.0.2, sharp@0.29.3 platform=linux arch=x64
[20:32:16.606]   • install prebuilt binary  name=sharp version=0.29.3 platform=linux arch=x64 napi= 
[20:32:16.733]   • rebuilding native dependency  name=register-scheme version=0.0.2
[20:32:18.745] Done in 39.72s.
[20:32:18.973] Running "yarn run build"
[20:32:19.151] yarn run v1.22.19
[20:32:19.177] $ vue-cli-service build
[20:32:19.777] Browserslist: caniuse-lite is outdated. Please run:
[20:32:19.777]   npx browserslist@latest --update-db
[20:32:19.778]   Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[20:32:19.812] 
[20:32:19.812] -  Building for production...
[20:32:22.184] Browserslist: caniuse-lite is outdated. Please run:
[20:32:22.184]   npx browserslist@latest --update-db
[20:32:22.185]   Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[20:32:22.205] (node:681) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
[20:32:22.205] (Use `node --trace-deprecation ...` to show where the warning was created)
[20:32:43.729]  WARNING  Compiled with 15 warnings12:32:43 PM
[20:32:43.731] 
[20:32:43.732] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.732] 
[20:32:43.733] /vercel/path0/src/components/CoverRow.vue
[20:32:43.733]   33:17  warning  'v-html' directive can lead to XSS attack  vue/no-v-html
[20:32:43.733] 
[20:32:43.734] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.734] 
[20:32:43.734] 
[20:32:43.735] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.735] 
[20:32:43.735] /vercel/path0/src/components/Modal.vue
[20:32:43.736]   25:5  warning  Prop 'close' requires default value to be set  vue/require-default-prop
[20:32:43.736] 
[20:32:43.736] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.737] 
[20:32:43.737] 
[20:32:43.737] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.738] 
[20:32:43.738] /vercel/path0/src/components/MvRow.vue
[20:32:43.738]   23:29  warning  'v-html' directive can lead to XSS attack    vue/no-v-html
[20:32:43.739]   33:5   warning  Prop 'mvs' requires default value to be set  vue/require-default-prop
[20:32:43.739] 
[20:32:43.739] ✖ 2 problems (0 errors, 2 warnings)
[20:32:43.740] 
[20:32:43.740] 
[20:32:43.740] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.741] 
[20:32:43.741] /vercel/path0/src/components/Navbar.vue
[20:32:43.741]   50:11  warning  Attribute "loading" should go before "@click"  vue/attributes-order
[20:32:43.742] 
[20:32:43.742] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.742]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:32:43.743] 
[20:32:43.743] 
[20:32:43.743] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.744] 
[20:32:43.744] /vercel/path0/src/components/TrackListItem.vue
[20:32:43.744]   98:5  warning  Prop 'trackProp' requires default value to be set  vue/require-default-prop
[20:32:43.745] 
[20:32:43.745] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.745] 
[20:32:43.746] 
[20:32:43.747] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.747] 
[20:32:43.747] /vercel/path0/src/views/loginAccount.vue
[20:32:43.748]   105:9  warning  'v-html' directive can lead to XSS attack  vue/no-v-html
[20:32:43.748] 
[20:32:43.748] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.749] 
[20:32:43.749] 
[20:32:43.749] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:32:43.750] 
[20:32:43.750] /vercel/path0/src/views/mv.vue
[20:32:43.751]   59:9  warning  Property name "mv" is not PascalCase  vue/component-definition-name-casing
[20:32:43.751] 
[20:32:43.751] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.751]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:32:43.751] 
[20:32:43.751] 
[20:32:43.751] You may use special comments to disable some warnings.
[20:32:43.751] Use // eslint-disable-next-line to ignore the next line.
[20:32:43.751] Use /* eslint-disable */ to ignore all warnings in a file.
[20:32:43.751]  warning  in ./src/assets/icons/index.js
[20:32:43.751] 
[20:32:43.751] Module Warning (from ./node_modules/thread-loader/dist/cjs.js):
[20:32:43.751] 
[20:32:43.751] /vercel/path0/src/assets/icons/index.js
[20:32:43.751]   4:15  warning  Property name "svg-icon" is not PascalCase  vue/component-definition-name-casing
[20:32:43.751] 
[20:32:43.751] ✖ 1 problem (0 errors, 1 warning)
[20:32:43.751]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:32:43.751] 
[20:32:43.751] 
[20:32:43.751]  @ ./src/main.js 16:0-24
[20:32:43.751]  @ multi ./src/main.js
[20:32:43.751] 
[20:32:43.751]  warning  
[20:32:43.751] 
[20:32:43.751] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:32:43.751] Conflicting order. Following module has been added:
[20:32:43.751]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ArtistsInLine.vue?vue&type=style&index=0&id=b3c008da&lang=scss&scoped=true&
[20:32:43.751] despite it was not able to fulfill desired ordering with these modules:
[20:32:43.751]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/CoverRow.vue?vue&type=style&index=0&id=46c6acd8&lang=scss&scoped=true&
[20:32:43.752]    - couldn't fulfill desired order of chunk group(s) 
[20:32:43.752]    - while fulfilling desired order of chunk group(s) , , , , 
[20:32:43.752]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/Cover.vue?vue&type=style&index=0&id=402f8e43&lang=scss&scoped=true&
[20:32:43.752]    - couldn't fulfill desired order of chunk group(s) 
[20:32:43.752]    - while fulfilling desired order of chunk group(s) , , , , , , 
[20:32:43.752]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:32:43.752]    - couldn't fulfill desired order of chunk group(s) , , , 
[20:32:43.752] 
[20:32:43.752]  warning  
[20:32:43.752] 
[20:32:43.752] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:32:43.752] Conflicting order. Following module has been added:
[20:32:43.752]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/TrackListItem.vue?vue&type=style&index=0&id=24ccaa64&lang=scss&scoped=true&
[20:32:43.752] despite it was not able to fulfill desired ordering with these modules:
[20:32:43.752]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:32:43.752]    - couldn't fulfill desired order of chunk group(s) , , , 
[20:32:43.752] 
[20:32:43.752]  warning  
[20:32:43.752] 
[20:32:43.752] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:32:43.753] Conflicting order. Following module has been added:
[20:32:43.753]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/Cover.vue?vue&type=style&index=0&id=402f8e43&lang=scss&scoped=true&
[20:32:43.753] despite it was not able to fulfill desired ordering with these modules:
[20:32:43.753]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:32:43.753]    - couldn't fulfill desired order of chunk group(s) , 
[20:32:43.753]    - while fulfilling desired order of chunk group(s) , 
[20:32:43.753]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:32:43.753]    - couldn't fulfill desired order of chunk group(s) , , , , 
[20:32:43.753] 
[20:32:43.753]  warning  
[20:32:43.753] 
[20:32:43.754] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:32:43.754] Conflicting order. Following module has been added:
[20:32:43.754]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/CoverRow.vue?vue&type=style&index=0&id=46c6acd8&lang=scss&scoped=true&
[20:32:43.754] despite it was not able to fulfill desired ordering with these modules:
[20:32:43.754]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:32:43.754]    - couldn't fulfill desired order of chunk group(s) , 
[20:32:43.754]    - while fulfilling desired order of chunk group(s) , 
[20:32:43.754]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:32:43.754]    - couldn't fulfill desired order of chunk group(s) , , 
[20:32:43.754] 
[20:32:43.754]  warning  
[20:32:43.754] 
[20:32:43.754] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:32:43.754] Conflicting order. Following module has been added:
[20:32:43.754]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:32:43.754] despite it was not able to fulfill desired ordering with these modules:
[20:32:43.754]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:32:43.754]    - couldn't fulfill desired order of chunk group(s) , 
[20:32:43.754] 
[20:32:43.755]  warning  
[20:32:43.755] 
[20:32:43.755] asset size limit: The following asset(s) exceed the recommended size limit (244 KiB).
[20:32:43.755] This can impact web performance.
[20:32:43.755] Assets: 
[20:32:43.756]   js/chunk-d87ab370.7129452a.js (371 KiB)
[20:32:43.756]   js/chunk-vendors.b1d9aaa8.js (617 KiB)
[20:32:43.756]   img/icons/1024x1024.png (381 KiB)
[20:32:43.756]   img/icons/icon.ico (353 KiB)
[20:32:43.756]   img/icons/icon.icns (765 KiB)
[20:32:43.756] 
[20:32:43.756]  warning  
[20:32:43.756] 
[20:32:43.756] entrypoint size limit: The following entrypoint(s) combined asset size exceeds the recommended limit (244 KiB). This can impact web performance.
[20:32:43.756] Entrypoints:
[20:32:43.756]   index (859 KiB)
[20:32:43.756]       css/chunk-vendors.46ab30d4.css
[20:32:43.756]       js/chunk-vendors.b1d9aaa8.js
[20:32:43.757]       css/index.705fc877.css
[20:32:43.757]       js/index.cbfa45ef.js
[20:32:43.757] 
[20:32:43.757] 
[20:32:43.790]   File                                      Size             Gzipped
[20:32:43.790] 
[20:32:43.790]   dist/js/chunk-vendors.b1d9aaa8.js         616.79 KiB       195.79 KiB
[20:32:43.790]   dist/js/chunk-d87ab370.7129452a.js        371.22 KiB       108.88 KiB
[20:32:43.790]   dist/js/index.cbfa45ef.js                 194.13 KiB       48.43 KiB
[20:32:43.790]   dist/precache-manifest.f3850649262575f    3.81 KiB         1.17 KiB
[20:32:43.790]   c6e1cd2a3a8835061.js
[20:32:43.790]   dist/service-worker.js                    1.04 KiB         0.61 KiB
[20:32:43.790]   dist/css/chunk-d87ab370.7c372010.css      81.10 KiB        12.54 KiB
[20:32:43.790]   dist/css/index.705fc877.css               29.76 KiB        5.71 KiB
[20:32:43.790]   dist/css/chunk-vendors.46ab30d4.css       18.29 KiB        3.61 KiB
[20:32:43.790] 
[20:32:43.790]   Images and other types of assets omitted.
[20:32:43.790] 
[20:32:43.790]  DONE  Build complete. The dist directory is ready to be deployed.
[20:32:43.790]  INFO  Check out deployment instructions at https://cli.vuejs.org/guide/deployment.html
[20:32:43.790]       
[20:32:44.804] Done in 25.66s.
[20:32:44.931] Build Completed in /vercel/output [1m]
[20:32:45.043] Deploying outputs...
[20:32:49.660] Deployment completed
[20:32:50.440] Creating build cache...