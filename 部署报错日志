[20:37:24.992] Running build in Washington, D.C., USA (East) – iad1
[20:37:25.006] Build machine configuration: 2 cores, 8 GB
[20:37:25.061] Cloning github.com/Lziu/YesPlayMusic (Branch: master, Commit: 88fefb3)
[20:37:25.292] Skipping build cache, deployment was triggered without cache.
[20:37:26.125] Cloning completed: 1.064s
[20:37:27.176] Running "vercel build"
[20:37:27.603] Vercel CLI 46.0.2
[20:37:28.250] Installing dependencies...
[20:37:28.662] yarn install v1.22.19
[20:37:28.762] [1/5] Validating package.json...
[20:37:28.765] [2/5] Resolving packages...
[20:37:29.447] warning Resolution field "degenerator@2.2.0" is incompatible with requested version "degenerator@^3.0.2"
[20:37:29.508] warning Resolution field "icon-gen@3.0.0" is incompatible with requested version "icon-gen@^2.0.0"
[20:37:29.904] [3/5] Fetching packages...
[20:37:50.363] [4/5] Linking dependencies...
[20:37:50.368] warning "svg-sprite-loader > svg-baker > postcss-prefix-selector@1.14.0" has incorrect peer dependency "postcss@7.x || 8.x".
[20:37:50.373] warning " > eslint-config-prettier@8.5.0" has incorrect peer dependency "eslint@>=7.0.0".
[20:37:50.377] warning " > sass-loader@10.2.1" has unmet peer dependency "webpack@^4.36.0 || ^5.0.0".
[20:37:59.487] [5/5] Building fresh packages...
[20:38:07.728] warning Error running install script for optional dependency: "/vercel/path0/node_modules/abstract-socket: Command failed.
[20:38:07.728] Exit code: 1
[20:38:07.729] Command: node-gyp rebuild
[20:38:07.729] Arguments: 
[20:38:07.729] Directory: /vercel/path0/node_modules/abstract-socket
[20:38:07.729] Output:
[20:38:07.730] gyp info it worked if it ends with ok
[20:38:07.730] gyp info using node-gyp@11.2.0
[20:38:07.730] gyp info using node@22.18.0 | linux | x64
[20:38:07.730] gyp info find Python using Python version 3.12.2 found at \"/usr/local/bin/python3\"
[20:38:07.730] 
[20:38:07.730] gyp http GET https://nodejs.org/download/release/v22.18.0/node-v22.18.0-headers.tar.gz
[20:38:07.731] gyp http 200 https://nodejs.org/download/release/v22.18.0/node-v22.18.0-headers.tar.gz
[20:38:07.731] gyp http GET https://nodejs.org/download/release/v22.18.0/SHASUMS256.txt
[20:38:07.731] gyp http 200 https://nodejs.org/download/release/v22.18.0/SHASUMS256.txt
[20:38:07.731] gyp info spawn /usr/local/bin/python3
[20:38:07.731] gyp info spawn args [
[20:38:07.731] gyp info spawn args '/node22/lib/node_modules/npm/node_modules/node-gyp/gyp/gyp_main.py',
[20:38:07.732] gyp info spawn args 'binding.gyp',
[20:38:07.732] gyp info spawn args '-f',
[20:38:07.732] gyp info spawn args 'make',
[20:38:07.732] gyp info spawn args '-I',
[20:38:07.732] gyp info spawn args '/vercel/path0/node_modules/abstract-socket/build/config.gypi',
[20:38:07.732] gyp info spawn args '-I',
[20:38:07.732] gyp info spawn args '/node22/lib/node_modules/npm/node_modules/node-gyp/addon.gypi',
[20:38:07.733] gyp info spawn args '-I',
[20:38:07.733] gyp info spawn args '/vercel/.cache/node-gyp/22.18.0/include/node/common.gypi',
[20:38:07.733] gyp info spawn args '-Dlibrary=shared_library',
[20:38:07.733] gyp info spawn args '-Dvisibility=default',
[20:38:07.733] gyp info spawn args '-Dnode_root_dir=/vercel/.cache/node-gyp/22.18.0',
[20:38:07.733] gyp info spawn args '-Dnode_gyp_dir=/node22/lib/node_modules/npm/node_modules/node-gyp',
[20:38:07.734] gyp info spawn args '-Dnode_lib_file=/vercel/.cache/node-gyp/22.18.0/<(target_arch)/node.lib',
[20:38:07.734] gyp info spawn args '-Dmodule_root_dir=/vercel/path0/node_modules/abstract-socket',
[20:38:07.734] gyp info spawn args '-Dnode_engine=v8',
[20:38:07.734] gyp info spawn args '--depth=.',
[20:38:07.734] gyp info spawn args '--no-parallel',
[20:38:07.734] gyp info spawn args '--generator-output',
[20:38:07.734] gyp info spawn args 'build',
[20:38:07.735] gyp info spawn args '-Goutput_dir=.'
[20:38:07.735] gyp info spawn args ]
[20:38:07.735] gyp info spawn make
[20:38:07.735] gyp info spawn args [ 'BUILDTYPE=Release', '-C', 'build' ]
[20:38:07.735] make: Entering directory '/vercel/path0/node_modules/abstract-socket/build'
[20:38:07.735]   CXX(target) Release/obj.target/bindings/src/abstract_socket.o
[20:38:07.736] In file included from ../../nan/nan.h:180,
[20:38:07.736]                  from ../src/abstract_socket.cc:5:
[20:38:07.739] ../../nan/nan_callbacks.h:55:23: error: 'AccessorSignature' is not a member of 'v8'
[20:38:07.740]    55 | typedef v8::Local<v8::AccessorSignature> Sig;
[20:38:07.740]       |                       ^~~~~~~~~~~~~~~~~
[20:38:07.740] ../../nan/nan_callbacks.h:55:40: error: template argument 1 is invalid
[20:38:07.740]    55 | typedef v8::Local<v8::AccessorSignature> Sig;
[20:38:07.740]       |                                        ^
[20:38:07.740] In file included from ../src/abstract_socket.cc:5:
[20:38:07.740] ../../nan/nan.h: In function 'void Nan::SetAccessor(v8::Local<v8::ObjectTemplate>, v8::Local<v8::String>, Nan::GetterCallback, Nan::SetterCallback, v8::Local<v8::Value>, v8::AccessControl, v8::PropertyAttribute, Nan::imp::Sig)':
[20:38:07.740] ../../nan/nan.h:2546:19: error: no matching function for call to 'v8::ObjectTemplate::SetAccessor(v8::Local<v8::String>&, void (*&)(v8::Local<v8::Name>, const v8::PropertyCallbackInfo<v8::Value>&), void (*&)(v8::Local<v8::Name>, v8::Local<v8::Value>, const v8::PropertyCallbackInfo<void>&), v8::Local<v8::Object>&, v8::AccessControl&, v8::PropertyAttribute&)'
[20:38:07.742]  2546 |   tpl->SetAccessor(
[20:38:07.742]       |   ~~~~~~~~~~~~~~~~^
[20:38:07.742]  2547 |       name
[20:38:07.742]       |       ~~~~         
[20:38:07.742]  2548 |     , getter_
[20:38:07.743]       |     ~~~~~~~~~      
[20:38:07.744]  2549 |     , setter_
[20:38:07.744]       |     ~~~~~~~~~      
[20:38:07.745]  2550 |     , obj
[20:38:07.745]       |     ~~~~~          
[20:38:07.745]  2551 |     , settings
[20:38:07.745]       |     ~~~~~~~~~~     
[20:38:07.749]  2552 |     , attribute
[20:38:07.750]       |     ~~~~~~~~~~~    
[20:38:07.750]  2553 | #if (NODE_MODULE_VERSION < NODE_18_0_MODULE_VERSION)
[20:38:07.750]       | ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[20:38:07.751]  2554 |     , signature
[20:38:07.751]       |     ~~~~~~~~~~~    
[20:38:07.751]  2555 | #endif
[20:38:07.751]       | ~~~~~~             
[20:38:07.751]  2556 |   );
[20:38:07.752]       |   ~                
[20:38:07.752] In file included from /vercel/.cache/node-gyp/22.18.0/include/node/v8-function.h:15,
[20:38:07.752]                  from /vercel/.cache/node-gyp/22.18.0/include/node/v8.h:33,
[20:38:07.752]                  from /vercel/.cache/node-gyp/22.18.0/include/node/node.h:74,
[20:38:07.752]                  from ../../nan/nan.h:60,
[20:38:07.752]                  from ../src/abstract_socket.cc:5:
[20:38:07.752] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1049:8: note: candidate: 'void v8::ObjectTemplate::SetAccessor(v8::Local<v8::String>, v8::AccessorGetterCallback, v8::AccessorSetterCallback, v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)'
[20:38:07.752]  1049 |   void SetAccessor(
[20:38:07.752]       |        ^~~~~~~~~~~
[20:38:07.752] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1052:61: note:   no known conversion for argument 5 from 'v8::AccessControl' to 'v8::PropertyAttribute'
[20:38:07.752]  1052 |       Local<Value> data = Local<Value>(), PropertyAttribute attribute = None,
[20:38:07.752]       |                                           ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~
[20:38:07.753] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1055:8: note: candidate: 'void v8::ObjectTemplate::SetAccessor(v8::Local<v8::Name>, v8::AccessorNameGetterCallback, v8::AccessorNameSetterCallback, v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)'
[20:38:07.753]  1055 |   void SetAccessor(
[20:38:07.753]       |        ^~~~~~~~~~~
[20:38:07.753] /vercel/.cache/node-gyp/22.18.0/include/node/v8-template.h:1058:61: note:   no known conversion for argument 5 from 'v8::AccessControl' to 'v8::PropertyAttribute'
[20:38:07.753]  1058 |       Local<Value> data = Local<Value>(), PropertyAttribute attribute = None,
[20:38:07.753]       |                                           ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~
[20:38:07.753] In file included from ../../nan/nan.h:60,
[20:38:07.753]                  from ../src/abstract_socket.cc:5:
[20:38:07.753] ../src/abstract_socket.cc: At global scope:
[20:38:07.753] /vercel/.cache/node-gyp/22.18.0/include/node/node.h:1228:7: warning: cast between incompatible function types from 'void (*)(v8::Local<v8::Object>)' to 'node::addon_register_func' {aka 'void (*)(v8::Local<v8::Object>, v8::Local<v8::Value>, void*)'} [-Wcast-function-type]
[20:38:07.753]  1228 |       (node::addon_register_func) (regfunc),                          \\\n      |       ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[20:38:07.753] /vercel/.cache/node-gyp/22.18.0/include/node/node.h:1262:3: note: in expansion of macro 'NODE_MODULE_X'
[20:38:07.753]  1262 |   NODE_MODULE_X(modname, regfunc, NULL, 0)  // NOLINT (readability/null_usage)
[20:38:07.753]       |   ^~~~~~~~~~~~~
[20:38:07.754] ../src/abstract_socket.cc:181:1: note: in expansion of macro 'NODE_MODULE'
[20:38:07.754]   181 | NODE_MODULE(abstract_socket, Initialize)
[20:38:07.754]       | ^~~~~~~~~~~
[20:38:07.754] In file included from /vercel/.cache/node-gyp/22.18.0/include/node/v8-array-buffer.h:12,
[20:38:07.758]                  from /vercel/.cache/node-gyp/22.18.0/include/node/v8.h:24,
[20:38:07.758]                  from /vercel/.cache/node-gyp/22.18.0/include/node/node.h:74,
[20:38:07.758]                  from ../../nan/nan.h:60,
[20:38:07.758]                  from ../src/abstract_socket.cc:5:
[20:38:07.759] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h: In instantiation of 'v8::Local<T>::Local(v8::Local<S>) [with S = v8::Data; T = v8::Value]':
[20:38:07.759] ../../nan/nan_callbacks_12_inl.h:175:53:   required from here
[20:38:07.759] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h:269:42: error: static assertion failed: type check
[20:38:07.759]   269 |     static_assert(std::is_base_of<T, S>::value, \"type check\");
[20:38:07.760]       |                                          ^~~~~
[20:38:07.760] /vercel/.cache/node-gyp/22.18.0/include/node/v8-local-handle.h:269:42: note: 'std::integral_constant<bool, false>::value' evaluates to false
[20:38:07.760] make: *** [bindings.target.mk:112: Release/obj.target/bindings/src/abstract_socket.o] Error 1
[20:38:07.761] make: Leaving directory '/vercel/path0/node_modules/abstract-socket/build'
[20:38:07.761] gyp ERR! build error 
[20:38:07.761] gyp ERR! stack Error: `make` failed with exit code: 2
[20:38:07.761] gyp ERR! stack at ChildProcess.<anonymous> (/node22/lib/node_modules/npm/node_modules/node-gyp/lib/build.js:219:23)
[20:38:07.762] gyp ERR! System Linux 5.10.174
[20:38:07.762] gyp ERR! command \"/node22/bin/node\" \"/node22/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js\" \"rebuild\"
[20:38:07.762] gyp ERR! cwd /vercel/path0/node_modules/abstract-socket
[20:38:07.762] gyp ERR! node -v v22.18.0
[20:38:07.763] gyp ERR! node-gyp -v v11.2.0
[20:38:07.763] gyp ERR! not ok"
[20:38:07.770] info This module is OPTIONAL, you can safely ignore this error
[20:38:08.813] success Saved lockfile.
[20:38:08.818] $ electron-builder install-app-deps
[20:38:09.091]   • electron-builder  version=23.0.3
[20:38:09.152]   • rebuilding native dependencies  dependencies=register-scheme@0.0.2, sharp@0.29.3 platform=linux arch=x64
[20:38:09.153]   • install prebuilt binary  name=sharp version=0.29.3 platform=linux arch=x64 napi= 
[20:38:09.288]   • rebuilding native dependency  name=register-scheme version=0.0.2
[20:38:11.612] Done in 42.95s.
[20:38:11.727] Running "yarn run build"
[20:38:11.913] yarn run v1.22.19
[20:38:11.956] $ vue-cli-service build
[20:38:12.579] Browserslist: caniuse-lite is outdated. Please run:
[20:38:12.579]   npx browserslist@latest --update-db
[20:38:12.579]   Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[20:38:12.615] 
[20:38:12.615] -  Building for production...
[20:38:14.963] Browserslist: caniuse-lite is outdated. Please run:
[20:38:14.964]   npx browserslist@latest --update-db
[20:38:14.964]   Why you should do it regularly: https://github.com/browserslist/browserslist#browsers-data-updating
[20:38:14.977] (node:697) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
[20:38:14.978] (Use `node --trace-deprecation ...` to show where the warning was created)
[20:38:38.792]  WARNING  Compiled with 15 warnings12:38:38 PM
[20:38:38.794] 
[20:38:38.796] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.796] 
[20:38:38.796] /vercel/path0/src/components/CoverRow.vue
[20:38:38.797]   33:17  warning  'v-html' directive can lead to XSS attack  vue/no-v-html
[20:38:38.797] 
[20:38:38.797] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.797] 
[20:38:38.798] 
[20:38:38.798] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.799] 
[20:38:38.799] /vercel/path0/src/components/Modal.vue
[20:38:38.799]   25:5  warning  Prop 'close' requires default value to be set  vue/require-default-prop
[20:38:38.799] 
[20:38:38.800] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.800] 
[20:38:38.800] 
[20:38:38.801] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.801] 
[20:38:38.801] /vercel/path0/src/components/MvRow.vue
[20:38:38.802]   23:29  warning  'v-html' directive can lead to XSS attack    vue/no-v-html
[20:38:38.802]   33:5   warning  Prop 'mvs' requires default value to be set  vue/require-default-prop
[20:38:38.802] 
[20:38:38.802] ✖ 2 problems (0 errors, 2 warnings)
[20:38:38.802] 
[20:38:38.805] 
[20:38:38.807] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.807] 
[20:38:38.807] /vercel/path0/src/components/Navbar.vue
[20:38:38.807]   50:11  warning  Attribute "loading" should go before "@click"  vue/attributes-order
[20:38:38.808] 
[20:38:38.808] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.808]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:38:38.808] 
[20:38:38.811] 
[20:38:38.812] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.812] 
[20:38:38.812] /vercel/path0/src/components/TrackListItem.vue
[20:38:38.813]   98:5  warning  Prop 'trackProp' requires default value to be set  vue/require-default-prop
[20:38:38.813] 
[20:38:38.814] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.814] 
[20:38:38.817] 
[20:38:38.818] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.818] 
[20:38:38.818] /vercel/path0/src/views/loginAccount.vue
[20:38:38.819]   105:9  warning  'v-html' directive can lead to XSS attack  vue/no-v-html
[20:38:38.819] 
[20:38:38.819] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.819] 
[20:38:38.820] 
[20:38:38.820] Module Warning (from ./node_modules/eslint-loader/index.js):
[20:38:38.820] 
[20:38:38.820] /vercel/path0/src/views/mv.vue
[20:38:38.820]   59:9  warning  Property name "mv" is not PascalCase  vue/component-definition-name-casing
[20:38:38.821] 
[20:38:38.821] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.821]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:38:38.822] 
[20:38:38.822] 
[20:38:38.822] You may use special comments to disable some warnings.
[20:38:38.822] Use // eslint-disable-next-line to ignore the next line.
[20:38:38.823] Use /* eslint-disable */ to ignore all warnings in a file.
[20:38:38.826]  warning  in ./src/assets/icons/index.js
[20:38:38.827] 
[20:38:38.827] Module Warning (from ./node_modules/thread-loader/dist/cjs.js):
[20:38:38.827] 
[20:38:38.827] /vercel/path0/src/assets/icons/index.js
[20:38:38.828]   4:15  warning  Property name "svg-icon" is not PascalCase  vue/component-definition-name-casing
[20:38:38.828] 
[20:38:38.828] ✖ 1 problem (0 errors, 1 warning)
[20:38:38.828]   0 errors and 1 warning potentially fixable with the `--fix` option.
[20:38:38.829] 
[20:38:38.829] 
[20:38:38.829]  @ ./src/main.js 16:0-24
[20:38:38.829]  @ multi ./src/main.js
[20:38:38.830] 
[20:38:38.830]  warning  
[20:38:38.830] 
[20:38:38.830] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:38:38.831] Conflicting order. Following module has been added:
[20:38:38.837]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ArtistsInLine.vue?vue&type=style&index=0&id=b3c008da&lang=scss&scoped=true&
[20:38:38.838] despite it was not able to fulfill desired ordering with these modules:
[20:38:38.838]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/CoverRow.vue?vue&type=style&index=0&id=46c6acd8&lang=scss&scoped=true&
[20:38:38.839]    - couldn't fulfill desired order of chunk group(s) 
[20:38:38.839]    - while fulfilling desired order of chunk group(s) , , , , 
[20:38:38.839]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/Cover.vue?vue&type=style&index=0&id=402f8e43&lang=scss&scoped=true&
[20:38:38.840]    - couldn't fulfill desired order of chunk group(s) 
[20:38:38.840]    - while fulfilling desired order of chunk group(s) , , , , , , 
[20:38:38.840]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:38:38.840]    - couldn't fulfill desired order of chunk group(s) , , , 
[20:38:38.841] 
[20:38:38.841]  warning  
[20:38:38.841] 
[20:38:38.842] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:38:38.842] Conflicting order. Following module has been added:
[20:38:38.842]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/TrackListItem.vue?vue&type=style&index=0&id=24ccaa64&lang=scss&scoped=true&
[20:38:38.842] despite it was not able to fulfill desired ordering with these modules:
[20:38:38.843]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:38:38.843]    - couldn't fulfill desired order of chunk group(s) , , , 
[20:38:38.843] 
[20:38:38.843]  warning  
[20:38:38.843] 
[20:38:38.844] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:38:38.844] Conflicting order. Following module has been added:
[20:38:38.844]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/Cover.vue?vue&type=style&index=0&id=402f8e43&lang=scss&scoped=true&
[20:38:38.844] despite it was not able to fulfill desired ordering with these modules:
[20:38:38.845]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:38:38.845]    - couldn't fulfill desired order of chunk group(s) , 
[20:38:38.845]    - while fulfilling desired order of chunk group(s) , 
[20:38:38.846]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:38:38.846]    - couldn't fulfill desired order of chunk group(s) , , , , 
[20:38:38.846] 
[20:38:38.846]  warning  
[20:38:38.847] 
[20:38:38.847] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:38:38.847] Conflicting order. Following module has been added:
[20:38:38.847]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/CoverRow.vue?vue&type=style&index=0&id=46c6acd8&lang=scss&scoped=true&
[20:38:38.848] despite it was not able to fulfill desired ordering with these modules:
[20:38:38.849]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:38:38.849]    - couldn't fulfill desired order of chunk group(s) , 
[20:38:38.849]    - while fulfilling desired order of chunk group(s) , 
[20:38:38.850]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:38:38.850]    - couldn't fulfill desired order of chunk group(s) , , 
[20:38:38.850] 
[20:38:38.850]  warning  
[20:38:38.851] 
[20:38:38.851] chunk chunk-d87ab370 [mini-css-extract-plugin]
[20:38:38.851] Conflicting order. Following module has been added:
[20:38:38.851]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/MvRow.vue?vue&type=style&index=0&id=4d5c11f6&lang=scss&scoped=true&
[20:38:38.852] despite it was not able to fulfill desired ordering with these modules:
[20:38:38.852]  * css ./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--8-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/components/ButtonTwoTone.vue?vue&type=style&index=0&id=34c86e99&lang=scss&scoped=true&
[20:38:38.852]    - couldn't fulfill desired order of chunk group(s) , 
[20:38:38.854] 
[20:38:38.860]  warning  
[20:38:38.861] 
[20:38:38.861] asset size limit: The following asset(s) exceed the recommended size limit (244 KiB).
[20:38:38.861] This can impact web performance.
[20:38:38.861] Assets: 
[20:38:38.861]   js/chunk-d87ab370.2ed805b1.js (371 KiB)
[20:38:38.861]   js/chunk-vendors.b1d9aaa8.js (617 KiB)
[20:38:38.861]   img/icons/1024x1024.png (381 KiB)
[20:38:38.861]   img/icons/icon.ico (353 KiB)
[20:38:38.862]   img/icons/icon.icns (765 KiB)
[20:38:38.862] 
[20:38:38.862]  warning  
[20:38:38.862] 
[20:38:38.862] entrypoint size limit: The following entrypoint(s) combined asset size exceeds the recommended limit (244 KiB). This can impact web performance.
[20:38:38.862] Entrypoints:
[20:38:38.862]   index (859 KiB)
[20:38:38.862]       css/chunk-vendors.46ab30d4.css
[20:38:38.862]       js/chunk-vendors.b1d9aaa8.js
[20:38:38.862]       css/index.705fc877.css
[20:38:38.862]       js/index.55dbe43a.js
[20:38:38.862] 
[20:38:38.862] 
[20:38:38.918]   File                                      Size             Gzipped
[20:38:38.918] 
[20:38:38.919]   dist/js/chunk-vendors.b1d9aaa8.js         616.79 KiB       195.79 KiB
[20:38:38.919]   dist/js/chunk-d87ab370.2ed805b1.js        371.22 KiB       108.87 KiB
[20:38:38.919]   dist/js/index.55dbe43a.js                 194.13 KiB       48.43 KiB
[20:38:38.920]   dist/precache-manifest.7fb4f9ff6c8b5db    3.81 KiB         1.18 KiB
[20:38:38.920]   05ac19e781610d5ec.js
[20:38:38.920]   dist/service-worker.js                    1.04 KiB         0.61 KiB
[20:38:38.920]   dist/css/chunk-d87ab370.7c372010.css      81.10 KiB        12.54 KiB
[20:38:38.921]   dist/css/index.705fc877.css               29.76 KiB        5.71 KiB
[20:38:38.921]   dist/css/chunk-vendors.46ab30d4.css       18.29 KiB        3.61 KiB
[20:38:38.921] 
[20:38:38.921]   Images and other types of assets omitted.
[20:38:38.922] 
[20:38:38.923]  DONE  Build complete. The dist directory is ready to be deployed.
[20:38:38.923]  INFO  Check out deployment instructions at https://cli.vuejs.org/guide/deployment.html
[20:38:38.924]       
[20:38:39.839] Done in 27.93s.
[20:38:39.979] Build Completed in /vercel/output [1m]
[20:38:40.096] Deploying outputs...
[20:38:43.300] Deployment completed
[20:38:44.113] Creating build cache...
[20:39:10.353] Created build cache: 26.232s
[20:39:10.354] Uploading build cache [201.70 MB]
[20:39:12.868] Build cache uploaded: 2.522s